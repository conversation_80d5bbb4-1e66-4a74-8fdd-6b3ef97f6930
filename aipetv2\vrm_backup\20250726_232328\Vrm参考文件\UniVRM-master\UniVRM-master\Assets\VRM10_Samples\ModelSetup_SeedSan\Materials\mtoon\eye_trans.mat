%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!21 &2100000
Material:
  serializedVersion: 6
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: eye_trans
  m_Shader: {fileID: 4800000, guid: e0edbf68d81d1f340ae8b110086b7063, type: 3}
  m_ShaderKeywords: _ALPHABLEND_ON
  m_LightmapFlags: 4
  m_EnableInstancingVariants: 0
  m_DoubleSidedGI: 0
  m_CustomRenderQueue: 3000
  stringTagMap:
    RenderType: Transparent
  disabledShaderPasses: []
  m_SavedProperties:
    serializedVersion: 3
    m_TexEnvs:
    - _BumpMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailAlbedoMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailMask:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailNormalMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _EmissionMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MainTex:
        m_Texture: {fileID: 2800000, guid: f3583f6d4db1971498af75d88b84c656, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MatcapTex:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MetallicGlossMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _OcclusionMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _OutlineWidthTex:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _OutlineWidthTexture:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _ParallaxMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _ReceiveShadowTexture:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _RimTex:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _RimTexture:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _ShadeTex:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _ShadeTexture:
        m_Texture: {fileID: 2800000, guid: f3583f6d4db1971498af75d88b84c656, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _ShadingGradeTexture:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _ShadingShiftTex:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _SphereAdd:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _UvAnimMaskTex:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _UvAnimMaskTexture:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    m_Floats:
    - _AlphaMode: 2
    - _AlphaToMask: 0
    - _BlendMode: 2
    - _BumpScale: 1
    - _CullMode: 2
    - _Cutoff: 0.5
    - _DebugMode: 0
    - _DetailNormalMapScale: 1
    - _DoubleSided: 0
    - _DstBlend: 10
    - _GiEqualization: 0.5
    - _GlossMapScale: 1
    - _Glossiness: 0
    - _GlossyReflections: 1
    - _IndirectLightIntensity: 0.1
    - _LightColorAttenuation: 0
    - _MToonVersion: 37
    - _M_AlphaToMask: 0
    - _M_CullMode: 2
    - _M_DebugMode: 0
    - _M_DstBlend: 10
    - _M_EditMode: 1
    - _M_SrcBlend: 5
    - _M_ZWrite: 0
    - _Metallic: 0
    - _Mode: 0
    - _OcclusionStrength: 1
    - _OutlineColorMode: 0
    - _OutlineCullMode: 1
    - _OutlineLightingMix: 1
    - _OutlineScaledMaxDistance: 1
    - _OutlineWidth: 0.5
    - _OutlineWidthMode: 0
    - _Parallax: 0.02
    - _ReceiveShadowRate: 1
    - _RenderQueueOffset: 0
    - _RimFresnelPower: 1
    - _RimLift: 0
    - _RimLightingMix: 1
    - _ShadeShift: 0
    - _ShadeToony: 0.5
    - _ShadingGradeRate: 1
    - _ShadingShiftFactor: -0.2
    - _ShadingShiftTexScale: 1
    - _ShadingToonyFactor: 0.8
    - _SmoothnessTextureChannel: 0
    - _SpecularHighlights: 1
    - _SrcBlend: 5
    - _TransparentWithZWrite: 0
    - _UVSec: 0
    - _UvAnimRotation: 0
    - _UvAnimRotationSpeed: 0
    - _UvAnimScrollX: 0
    - _UvAnimScrollXSpeed: 0
    - _UvAnimScrollY: 0
    - _UvAnimScrollYSpeed: 0
    - _ZWrite: 0
    m_Colors:
    - _Color: {r: 1, g: 1, b: 1, a: 1}
    - _EmissionColor: {r: 0, g: 0, b: 0, a: 1}
    - _MatcapColor: {r: 0, g: 0, b: 0, a: 1}
    - _OutlineColor: {r: 0, g: 0, b: 0, a: 1}
    - _RimColor: {r: 0, g: 0, b: 0, a: 1}
    - _ShadeColor: {r: 1, g: 1, b: 1, a: 1}
  m_BuildTextureStacks: []
